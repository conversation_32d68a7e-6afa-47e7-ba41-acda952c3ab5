<?php
// Test script to verify PHP 8.2 compatibility
echo "PHP Version: " . PHP_VERSION . "\n";
echo "PHP Major Version: " . PHP_MAJOR_VERSION . "\n";
echo "PHP Minor Version: " . PHP_MINOR_VERSION . "\n";

// Test curl availability
if (function_exists('curl_init')) {
    echo "✓ cURL is available\n";
    
    // Test curl version
    $curlVersion = curl_version();
    echo "cURL Version: " . $curlVersion['version'] . "\n";
    echo "SSL Version: " . $curlVersion['ssl_version'] . "\n";
} else {
    echo "✗ cURL is NOT available\n";
}

// Test JSON functions
if (function_exists('json_decode') && function_exists('json_encode')) {
    echo "✓ JSON functions are available\n";
} else {
    echo "✗ JSON functions are NOT available\n";
}

// Test session functions
if (function_exists('session_start')) {
    echo "✓ Session functions are available\n";
} else {
    echo "✗ Session functions are NOT available\n";
}

// Test hash functions
if (function_exists('hash') && function_exists('hash_equals')) {
    echo "✓ Hash functions are available\n";
} else {
    echo "✗ Hash functions are NOT available\n";
}

// Test parse_ini_file
if (function_exists('parse_ini_file')) {
    echo "✓ parse_ini_file is available\n";
    
    // Test .env file reading
    if (file_exists('.env')) {
        $env = parse_ini_file('.env');
        if ($env !== false && isset($env['API_KEY'])) {
            echo "✓ .env file can be read successfully\n";
        } else {
            echo "✗ .env file cannot be read or API_KEY not found\n";
        }
    } else {
        echo "✗ .env file does not exist\n";
    }
} else {
    echo "✗ parse_ini_file is NOT available\n";
}

echo "\nPHP 8.2 Compatibility Test Complete!\n";
?>
