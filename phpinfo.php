<?php
// PHP Information and Extension Check
echo "<h1>PHP Configuration Check</h1>";
echo "<h2>PHP Version: " . PHP_VERSION . "</h2>";

echo "<h3>Required Extensions Status:</h3>";
echo "<ul>";

// Check cURL
if (extension_loaded('curl')) {
    echo "<li style='color: green;'>✓ cURL: ENABLED</li>";
    $curl_info = curl_version();
    echo "<ul><li>Version: " . $curl_info['version'] . "</li>";
    echo "<li>SSL: " . $curl_info['ssl_version'] . "</li></ul>";
} else {
    echo "<li style='color: red;'>✗ cURL: DISABLED</li>";
}

// Check JSON
if (extension_loaded('json')) {
    echo "<li style='color: green;'>✓ JSON: ENABLED</li>";
} else {
    echo "<li style='color: red;'>✗ JSON: DISABLED</li>";
}

// Check Session
if (extension_loaded('session')) {
    echo "<li style='color: green;'>✓ Session: ENABLED</li>";
} else {
    echo "<li style='color: red;'>✗ Session: DISABLED</li>";
}

// Check Hash
if (extension_loaded('hash')) {
    echo "<li style='color: green;'>✓ Hash: ENABLED</li>";
} else {
    echo "<li style='color: red;'>✗ Hash: DISABLED</li>";
}

echo "</ul>";

echo "<h3>PHP Configuration File (php.ini) Location:</h3>";
echo "<p><strong>" . php_ini_loaded_file() . "</strong></p>";

echo "<h3>Loaded Extensions:</h3>";
$extensions = get_loaded_extensions();
sort($extensions);
echo "<p>" . implode(', ', $extensions) . "</p>";

echo "<hr>";
echo "<h3>Full PHP Info:</h3>";
phpinfo();
?>
