<?php
// Start session for CSRF protection
session_start();

// Function to make HTTP request with fallback
function makeHttpRequest($url, $postData, $headers) {
    // Try cURL first if available
    if (function_exists('curl_init')) {
        $curl = curl_init();
        if ($curl === false) {
            return ['success' => false, 'error' => 'Failed to initialize cURL'];
        }

        $curlOptions = [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => $postData,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2,
            CURLOPT_USERAGENT => "PHP-cURL/8.2 RajaOngkir-Client",
            CURLOPT_FOLLOWLOCATION => true
        ];

        if (!curl_setopt_array($curl, $curlOptions)) {
            curl_close($curl);
            return ['success' => false, 'error' => 'Failed to set cURL options'];
        }

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $curlError = curl_error($curl);
        
        curl_close($curl);

        if ($response === false || !empty($curlError)) {
            return ['success' => false, 'error' => "cURL Error: $curlError"];
        } elseif ($httpCode !== 200) {
            return ['success' => false, 'error' => "HTTP Error: HTTP $httpCode"];
        }

        return ['success' => true, 'data' => $response];
    }
    
    // Fallback to file_get_contents with stream context
    $headerString = implode("\r\n", $headers);
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => $headerString,
            'content' => $postData,
            'timeout' => 30,
            'ignore_errors' => true
        ],
        'ssl' => [
            'verify_peer' => true,
            'verify_peer_name' => true
        ]
    ]);

    $response = @file_get_contents($url, false, $context);
    
    if ($response === false) {
        $error = error_get_last();
        return ['success' => false, 'error' => 'file_get_contents failed: ' . ($error['message'] ?? 'Unknown error')];
    }

    // Check HTTP response code
    if (isset($http_response_header)) {
        $statusLine = $http_response_header[0];
        if (!preg_match('/HTTP\/\d\.\d\s+200/', $statusLine)) {
            return ['success' => false, 'error' => "HTTP Error: $statusLine"];
        }
    }

    return ['success' => true, 'data' => $response];
}
?>
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Cekresi RajaOngkir (Fallback Version)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.9.0/css/all.min.css">
  </head>
  <body>
    <nav class="navbar bg-body-tertiary">
        <div class="container">
            <a class="navbar-brand">Cekresi RajaOngkir (Fallback Version)</a>
            <a href="https://github.com/renannazar" class="text-dark" target="_blank">
                <i class="fab fa-github"></i>
            </a>
        </div>
    </nav>

    <div class="row justify-content-center mt-5">
        <div class="col-md-5">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Fallback Version:</strong> This version works with or without cURL extension.
                <?php if (function_exists('curl_init')): ?>
                    <br><span class="text-success">✓ cURL is available and will be used.</span>
                <?php else: ?>
                    <br><span class="text-warning">⚠ cURL not available, using file_get_contents fallback.</span>
                <?php endif; ?>
            </div>

            <p class="bg-primary p-3 rounded text-white">
                <i class="fas fa-info"></i>
                Aplikasi untuk cekresi dengan menggunakan API RajaOngkir. Setting API ada di .env pada file program ini.
            </p>

            <form method="POST" novalidate>
              <div class="mb-3">
                <label for="no_resi" class="form-label">Nomor Resi</label>
                <textarea 
                  class="form-control" 
                  rows="6" 
                  name="no_resi" 
                  id="no_resi"
                  placeholder="Masukkan nomor resi, pisahkan antara satu resi dengan koma"
                  required
                  maxlength="1000"
                ><?= htmlspecialchars($_POST['no_resi'] ?? '') ?></textarea>
              </div>
              
              <div class="mb-3">
                <label for="kurir" class="form-label">Pilih Kurir</label>
                <select name="kurir" id="kurir" class="form-control" required>
                  <option value="jne" <?= (($_POST['kurir'] ?? 'jne') === 'jne') ? 'selected' : '' ?>>JNE</option>
                  <option value="jnt" <?= (($_POST['kurir'] ?? '') === 'jnt') ? 'selected' : '' ?>>JNT</option>
                  <option value="pos" <?= (($_POST['kurir'] ?? '') === 'pos') ? 'selected' : '' ?>>POS INDONESIA</option>
                </select>
              </div>
              
              <div class="d-grid">
                <button name="submit" type="submit" class="btn btn-success">
                  <i class="fas fa-search"></i> Cek Resi
                </button>
              </div>
              
              <input type="hidden" name="csrf_token" value="<?= hash('sha256', session_id() . 'cekresi_form') ?>">
            </form>

            <?php
            // Load environment variables
            $env = parse_ini_file('.env');
            if ($env === false || !isset($env['API_KEY'])) {
                echo '<div class="alert alert-danger mt-3">Error: Unable to load API key from .env file</div>';
                exit;
            }
            $apiKey = trim($env['API_KEY']);

            $dataAll = [];
            $errors = [];

            if (isset($_POST['submit']) && isset($_POST['no_resi']) && !empty(trim($_POST['no_resi']))) {
                // CSRF validation
                $expectedToken = hash('sha256', session_id() . 'cekresi_form');
                $submittedToken = $_POST['csrf_token'] ?? '';
                
                if (!hash_equals($expectedToken, $submittedToken)) {
                    $errors[] = "Invalid form submission. Please try again.";
                } else {
                    $noResiInput = trim($_POST['no_resi']);
                    $kurir = isset($_POST['kurir']) ? trim($_POST['kurir']) : 'jne';
                    
                    $allowedCouriers = ['jne', 'jnt', 'pos'];
                    if (!in_array($kurir, $allowedCouriers)) {
                        $kurir = 'jne';
                    }
                    
                    $dataResi = array_filter(array_map('trim', explode(',', $noResiInput)));

                    foreach ($dataResi as $resi) {
                        if (empty($resi)) continue;
                        
                        $postData = http_build_query([
                            'waybill' => $resi,
                            'courier' => $kurir
                        ]);
                        
                        $headers = [
                            "Content-Type: application/x-www-form-urlencoded",
                            "key: $apiKey"
                        ];

                        $result = makeHttpRequest("https://pro.rajaongkir.com/api/waybill", $postData, $headers);
                        
                        if (!$result['success']) {
                            $errors[] = "Error for resi $resi: " . $result['error'];
                        } else {
                            $jsonData = json_decode($result['data']);
                            if (json_last_error() === JSON_ERROR_NONE) {
                                $dataAll[] = $result['data'];
                            } else {
                                $errors[] = "Invalid JSON response for resi $resi: " . json_last_error_msg();
                            }
                        }
                    }
                }
            }

            // Display errors
            if (!empty($errors)) {
                echo '<div class="alert alert-danger mt-3">';
                echo '<strong>Errors occurred:</strong><ul>';
                foreach ($errors as $error) {
                    echo '<li>' . htmlspecialchars($error) . '</li>';
                }
                echo '</ul></div>';
            }

            // Display results
            if (!empty($dataAll)):
            ?>
            <div class="mt-3">
              <?php foreach($dataAll as $data): ?>
              <?php 
                $dataJson = json_decode($data);
                if ($dataJson === null || !isset($dataJson->rajaongkir)) {
                    echo '<div class="alert alert-warning">Invalid response data received</div>';
                    continue;
                }
                $rajaongkir = $dataJson->rajaongkir;
              ?>
              <div class="card mb-4">
                <div class="card-header text-uppercase">
                  No Resi : <?= htmlspecialchars($rajaongkir->query->waybill ?? 'N/A') ?>, 
                  <b>Kurir : <?= htmlspecialchars(strtoupper($rajaongkir->query->courier ?? 'N/A')) ?></b>
                </div>
                <div class="card-body">
                  <?php if(!empty($rajaongkir->result) && isset($rajaongkir->result->delivery_status)):  ?>
                    <strong>Status Paket:</strong> <?= htmlspecialchars($rajaongkir->result->delivery_status->status ?? 'N/A') ?> <br>
                    <strong>Penerima:</strong> <?= htmlspecialchars($rajaongkir->result->delivery_status->pod_receiver ?? 'N/A') ?> <br>
                    <strong>Tanggal:</strong> <?= htmlspecialchars($rajaongkir->result->delivery_status->pod_date ?? 'N/A') ?> | 
                    <?= htmlspecialchars($rajaongkir->result->delivery_status->pod_time ?? 'N/A') ?> <br>
                  <?php else: ?>
                    <div class="alert alert-warning">
                      <?= htmlspecialchars($rajaongkir->status->description ?? 'No tracking information available') ?>
                    </div>
                  <?php endif?>
                </div>
              </div>
              <?php endforeach ?>
            </div>
            <?php endif ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  </body>
</html>
