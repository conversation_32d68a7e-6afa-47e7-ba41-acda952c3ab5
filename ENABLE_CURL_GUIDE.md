# How to Enable cURL in PHP (Laragon)

## Error Description
You're getting this error because the cURL extension is not enabled in your PHP installation:
```
Fatal error: Uncaught Error: Call to undefined function curl_init()
```

## Solution for Laragon Users

### Method 1: Using Laragon Menu (Recommended)
1. **Right-click on the Laragon tray icon** (usually in the bottom-right corner of your screen)
2. **Navigate to: PHP → Extensions**
3. **Find and click on "curl"** to enable it
4. **Restart Laragon** by right-clicking the tray icon and selecting "Stop All" then "Start All"

### Method 2: Manual php.ini Edit
1. **Right-click Laragon tray icon → PHP → php.ini**
2. **Find this line:** `;extension=curl`
3. **Remove the semicolon:** `extension=curl`
4. **Save the file**
5. **Restart Laragon**

### Method 3: Quick Menu Access
1. **Right-click Laragon tray icon → Quick app → php.ini**
2. **Search for "curl"** (Ctrl+F)
3. **Uncomment the line:** `;extension=curl` → `extension=curl`
4. **Save and restart Laragon**

## Verification Steps

### Step 1: Check if cURL is enabled
1. **Open your browser**
2. **Navigate to:** `http://localhost/cekresi/phpinfo.php`
3. **Look for cURL section** - it should show version information

### Step 2: Test the application
1. **Navigate to:** `http://localhost/cekresi/`
2. **The error should be gone**
3. **Try entering a test tracking number**

## Alternative Solutions

### If Laragon Method Doesn't Work

#### Check PHP Version
1. **Right-click Laragon → PHP → Version**
2. **Make sure you're using PHP 8.2**
3. **Switch if necessary and restart**

#### Manual Extension Check
1. **Open Command Prompt**
2. **Navigate to Laragon PHP directory:**
   ```
   cd C:\laragon\bin\php\php-8.2.x
   ```
3. **Check extensions:**
   ```
   php -m | findstr curl
   ```

#### Reinstall PHP Extensions
1. **In Laragon, right-click tray icon**
2. **Go to: Tools → Re-install All Services**
3. **Restart Laragon**

## Common Issues and Solutions

### Issue 1: Extension Not Found
- **Solution:** Update Laragon to the latest version
- **Alternative:** Download PHP manually and replace in Laragon

### Issue 2: Permission Errors
- **Solution:** Run Laragon as Administrator
- **Right-click Laragon → "Run as Administrator"**

### Issue 3: Multiple PHP Versions
- **Solution:** Ensure you're editing the correct php.ini
- **Check active PHP version in Laragon menu**

## Testing Commands

### Test cURL from Command Line
```bash
php -r "echo function_exists('curl_init') ? 'cURL is enabled' : 'cURL is disabled';"
```

### Test Full Application
```bash
php test_php_version.php
```

## Contact Information
If you continue to have issues:
1. **Check Laragon documentation:** https://laragon.org/docs/
2. **Verify PHP version compatibility**
3. **Consider using XAMPP or WAMP as alternative**

## Quick Fix Summary
1. **Laragon tray → PHP → Extensions → curl**
2. **Restart Laragon**
3. **Test at http://localhost/cekresi/**

That's it! Your cURL should now be working with PHP 8.2.
