<?php
// Start session for CSRF protection
session_start();
?>
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Cek<PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.9.0/css/all.min.css" integrity="sha512-q3eWabyZPc1XTCmF+8/LuE1ozpg5xxn7iO89yfSOd5/oKvyqLngoNGsx8jq92Y8eXJ/IRxQbEC+FGSYxtk2oiw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.9.0/css/brands.min.css" integrity="sha512-sKhd1NGM4i4pJj+3P+NVHisu2z5rKAwNG1IpWMdKsFWYlUHFSrsAO3geQ5QNKttkMPZNTo76tfg8jVx2ICP7qw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  </head>
  <body>
    <nav class="navbar bg-body-tertiary">
        <div class="container">
            <a class="navbar-brand">Cekresi RajaOngkir</a>
            <a href="https://github.com/renannazar" class="text-dark" target="_blank">
                <i class="fab fa-github"></i>
            </a>
        </div>
    </nav>

    <div class="row justify-content-center mt-5">
        <div class="col-md-5">
            <form method="POST" novalidate>
              <div class="mb-3">
                <label for="no_resi" class="form-label">Nomor Resi</label>
                <textarea
                  class="form-control"
                  rows="6"
                  name="no_resi"
                  id="no_resi"
                  placeholder="Masukkan nomor resi, pisahkan antara satu resi dengan koma"
                  required
                  maxlength="1000"
                  value="<?= htmlspecialchars($_POST['no_resi'] ?? '') ?>"
                ><?= htmlspecialchars($_POST['no_resi'] ?? '') ?></textarea>
                <div class="form-text">Maksimal 1000 karakter. Pisahkan multiple resi dengan koma.</div>
              </div>

              <div class="mb-3">
                <label for="kurir" class="form-label">Pilih Kurir</label>
                <select name="kurir" id="kurir" class="form-control" required>
                  <option value="jne" <?= (($_POST['kurir'] ?? 'jne') === 'jne') ? 'selected' : '' ?>>JNE</option>
                  <option value="jnt" <?= (($_POST['kurir'] ?? '') === 'jnt') ? 'selected' : '' ?>>JNT</option>
                  <option value="pos" <?= (($_POST['kurir'] ?? '') === 'pos') ? 'selected' : '' ?>>POS INDONESIA</option>
                </select>
              </div>

              <div class="d-grid">
                <button name="submit" type="submit" class="btn btn-success">
                  <i class="fas fa-search"></i> Cek Resi
                </button>
              </div>

              <!-- CSRF Protection (basic) -->
              <input type="hidden" name="csrf_token" value="<?= hash('sha256', session_id() . 'cekresi_form') ?>">
            </form>

            <?php
            // Error reporting for development (remove in production)
            error_reporting(E_ALL);
            ini_set('display_errors', 1);

            // Load environment variables with error handling
            $env = parse_ini_file('.env');
            if ($env === false || !isset($env['API_KEY'])) {
                die('Error: Unable to load API key from .env file');
            }
            $apiKey = trim($env['API_KEY']);

            // Initialize variables
            $dataAll = [];
            $errors = [];

            if (isset($_POST['submit']) && isset($_POST['no_resi']) && !empty(trim($_POST['no_resi']))) {
                // Basic CSRF validation
                $expectedToken = hash('sha256', session_id() . 'cekresi_form');
                $submittedToken = $_POST['csrf_token'] ?? '';

                if (!hash_equals($expectedToken, $submittedToken)) {
                    $errors[] = "Invalid form submission. Please try again.";
                } else {
                // Sanitize and validate input
                $noResiInput = trim($_POST['no_resi']);
                $kurir = isset($_POST['kurir']) ? trim($_POST['kurir']) : 'jne';

                // Validate courier
                $allowedCouriers = ['jne', 'jnt', 'pos'];
                if (!in_array($kurir, $allowedCouriers)) {
                    $kurir = 'jne'; // Default fallback
                }

                $dataResi = array_filter(array_map('trim', explode(',', $noResiInput)));

                foreach ($dataResi as $resi) {
                    if (empty($resi)) continue;

                    // Initialize curl with error checking
                    $curl = curl_init();
                    if ($curl === false) {
                        $errors[] = "Failed to initialize cURL for resi: $resi";
                        continue;
                    }

                    // Updated curl options for PHP 8.2 compatibility
                    $curlOptions = [
                        CURLOPT_URL => "https://pro.rajaongkir.com/api/waybill",
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => "", // Let curl choose the best encoding
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 30,
                        CURLOPT_CONNECTTIMEOUT => 10,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => "POST",
                        CURLOPT_POSTFIELDS => http_build_query([
                            'waybill' => $resi,
                            'courier' => $kurir
                        ]),
                        CURLOPT_HTTPHEADER => [
                            "Content-Type: application/x-www-form-urlencoded",
                            "key: $apiKey"
                        ],
                        CURLOPT_SSL_VERIFYPEER => true,
                        CURLOPT_SSL_VERIFYHOST => 2,
                        CURLOPT_USERAGENT => "PHP-cURL/8.2 RajaOngkir-Client",
                        CURLOPT_FOLLOWLOCATION => true
                    ];

                    if (!curl_setopt_array($curl, $curlOptions)) {
                        $errors[] = "Failed to set cURL options for resi: $resi";
                        curl_close($curl);
                        continue;
                    }

                    $response = curl_exec($curl);
                    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                    $curlError = curl_error($curl);
                    var_dump($response);

                    curl_close($curl);

                    if ($response === false || !empty($curlError)) {
                        $errors[] = "cURL Error for resi $resi: " . $curlError;
                    } elseif ($httpCode !== 200) {
                        $errors[] = "HTTP Error for resi $resi: HTTP $httpCode";
                    } else {
                        // Validate JSON response
                        $jsonData = json_decode($response);
                        if (json_last_error() === JSON_ERROR_NONE) {
                            $dataAll[] = $response;
                        } else {
                            $errors[] = "Invalid JSON response for resi $resi: " . json_last_error_msg();
                        }
                    }
                }
                } // End CSRF validation
            }

            // Display errors if any
            if (!empty($errors)) {
                echo '<div class="alert alert-danger mt-3">';
                echo '<strong>Errors occurred:</strong><ul>';
                foreach ($errors as $error) {
                    echo '<li>' . htmlspecialchars($error) . '</li>';
                }
                echo '</ul></div>';
            }
            ?>

            <?php
            if (!empty($dataAll)):
            ?>
            <div class="mt-3">
              <?php foreach($dataAll as $data): ?>
              <?php
                $dataJson = json_decode($data);
                if ($dataJson === null || !isset($dataJson->rajaongkir)) {
                    echo '<div class="alert alert-warning">Invalid response data received</div>';
                    continue;
                }
                $rajaongkir = $dataJson->rajaongkir;
              ?>
              <div class="card mb-4">
                <div class="card-header text-uppercase">
                  No Resi : <?= htmlspecialchars($rajaongkir->query->waybill ?? 'N/A') ?>,
                  <b>Kurir : <?= htmlspecialchars(strtoupper($rajaongkir->query->courier ?? 'N/A')) ?></b>
                </div>
                <div class="card-body">
                  <?php if(!empty($rajaongkir->result) && isset($rajaongkir->result->delivery_status)):  ?>
                    <strong>Status Paket:</strong> <?= htmlspecialchars($rajaongkir->result->delivery_status->status ?? 'N/A') ?> <br>
                    <strong>Penerima:</strong> <?= htmlspecialchars($rajaongkir->result->delivery_status->pod_receiver ?? 'N/A') ?> <br>
                    <strong>Tanggal:</strong> <?= htmlspecialchars($rajaongkir->result->delivery_status->pod_date ?? 'N/A') ?> |
                    <?= htmlspecialchars($rajaongkir->result->delivery_status->pod_time ?? 'N/A') ?> <br>

                    <?php if (!empty($rajaongkir->result->manifest)): ?>
                    <hr>
                    <strong>Tracking History:</strong>
                    <div class="mt-2">
                      <?php foreach ($rajaongkir->result->manifest as $manifest): ?>
                      <div class="border-start border-3 border-primary ps-3 mb-2">
                        <small class="text-muted"><?= htmlspecialchars($manifest->manifest_date ?? '') ?> <?= htmlspecialchars($manifest->manifest_time ?? '') ?></small><br>
                        <strong><?= htmlspecialchars($manifest->manifest_description ?? '') ?></strong><br>
                        <span class="text-muted"><?= htmlspecialchars($manifest->city_name ?? '') ?></span>
                      </div>
                      <?php endforeach; ?>
                    </div>
                    <?php endif; ?>

                  <?php else: ?>
                    <div class="alert alert-warning">
                      <?= htmlspecialchars($rajaongkir->status->description ?? 'No tracking information available') ?>
                    </div>
                  <?php endif?>
                </div>
              </div>
              <?php endforeach ?>
            </div>
            <?php endif ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
  </body>
</html>